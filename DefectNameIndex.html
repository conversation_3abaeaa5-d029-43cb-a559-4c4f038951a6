<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Root Cause Management - Summary, Full Table, Filters & AI</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f0f9ff; /* Lighter sky blue background - Tailwind sky-50 */
        }
        /* Custom scrollbar for webkit browsers */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #e0f2fe; /* Tailwind sky-100 */
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb {
            background: #7dd3fc; /* Tailwind sky-300 */
            border-radius: 10px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #38bdf8; /* Tailwind sky-400 */
        }
        /* Style for required asterisks */
        .required-asterisk {
            color: #f43f5e; /* Tailwind rose-500 */
        }
        /* Style for modal backdrop */
        .modal-backdrop {
            background-color: rgba(0, 0, 0, 0.65); /* Slightly darker backdrop */
        }
        /* Focus ring consistency - using cyan */
        select:focus, input:focus, textarea:focus, button:focus, .custom-dropdown-toggle:focus, .custom-dropdown-search-input:focus {
            outline: 2px solid transparent;
            outline-offset: 2px;
            --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
            --tw-ring-offset-width: 0px;
            --tw-ring-offset-color: #fff;
            --tw-ring-color: #22d3ee; /* Tailwind cyan-500 */
            --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
            --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
            box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
        }
        /* Custom transition for modal scale */
        .scale-in-center {
            animation: scaleInCenter 0.3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
        }
        .scale-out-center {
            animation: scaleOutCenter 0.3s cubic-bezier(0.550, 0.085, 0.680, 0.530) both;
        }
        @keyframes scaleInCenter {
            0% { transform: scale(0.9); opacity: 0; }
            100% { transform: scale(1); opacity: 1; }
        }
        @keyframes scaleOutCenter {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(0.9); opacity: 0; }
        }
        /* Loading Spinner */
        .loader, .inline-loader {
            border: 3px solid #f3f3f3; /* Light grey */
            border-top: 3px solid #06b6d4; /* Cyan */
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        .loader { /* For Preventive Actions Modal */
            width: 32px;
            height: 32px;
            margin: 20px auto;
        }
        .inline-loader { /* For Root Cause Elaboration Button */
            width: 16px;
            height: 16px;
            display: inline-block;
            margin-left: 8px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom Dropdown Styles */
        .custom-dropdown-options-list::-webkit-scrollbar {
            width: 6px;
        }
        .custom-dropdown-options-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        .custom-dropdown-options-list::-webkit-scrollbar-thumb {
            background: #cbd5e1; /* slate-300 */
            border-radius: 3px;
        }
        .custom-dropdown-options-list::-webkit-scrollbar-thumb:hover {
            background: #94a3b8; /* slate-500 */
        }
        .custom-dropdown-option.highlighted {
            background-color: #f0f9ff; /* sky-50 */
        }
         /* Summary Card Styles */
        .summary-card {
            background-color: white;
            border-radius: 0.75rem; /* rounded-xl */
            padding: 1.5rem; /* p-6 */
            box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05); /* shadow-xl */
            transition: all 0.3s ease-in-out;
        }
        .summary-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0,0,0,0.1), 0 10px 10px -5px rgba(0,0,0,0.04); /* shadow-2xl */
        }
        .summary-title {
            font-size: 0.875rem; /* text-sm */
            font-weight: 500; /* font-medium */
            color: #475569; /* slate-600 */
            margin-bottom: 0.25rem; /* mb-1 */
        }
        .summary-value {
            font-size: 1.875rem; /* text-3xl */
            font-weight: 700; /* font-bold */
            color: #0891b2; /* cyan-600 */
        }
    </style>
</head>
<body class="antialiased text-slate-700">

    <div class="container mx-auto p-4 sm:p-6 lg:p-8">
        <header class="mb-10">
            <h1 class="text-4xl font-bold text-cyan-700">Root Cause Management</h1>
        </header>

        <div id="overallSummarySection" class="mb-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div class="summary-card">
                <div class="summary-title">Total Categories</div>
                <div id="overallTotalCategories" class="summary-value">0</div>
            </div>
            <div class="summary-card">
                <div class="summary-title">Total Defect Groups</div>
                <div id="overallTotalDefectGroups" class="summary-value">0</div>
            </div>
            <div class="summary-card">
                <div class="summary-title">Total Active Root Causes</div>
                <div id="overallActiveCauses" class="summary-value text-green-600">0</div>
            </div>
            <div class="summary-card">
                <div class="summary-title">Total Inactive Root Causes</div>
                <div id="overallInactiveCauses" class="summary-value text-red-600">0</div>
            </div>
        </div>

        <div class="mb-8 p-6 bg-white rounded-xl shadow-lg border border-slate-200"> <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 md:gap-6">
                <div class="grid grid-cols-1 sm:grid-cols-2 md:flex-grow gap-4 md:gap-6">
                    <div class="w-full">
                        <label for="categorySearchFilter" class="block text-sm font-medium text-slate-700 mb-1">Category</label>
                        <div class="relative mt-1">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input type="text" id="categorySearchFilter" name="categorySearchFilter"
                                   placeholder="Search categories..."
                                   class="block w-full pl-10 pr-10 py-3 border border-slate-300 rounded-lg shadow-sm focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 sm:text-sm transition-all"
                                   autocomplete="off">
                            <button type="button" id="categoryClearBtn" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-150" style="display: none;">
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                            <div id="categoryDropdown" class="absolute z-20 mt-1 w-full bg-white border border-slate-300 rounded-lg shadow-lg hidden max-h-60 overflow-y-auto">
                                <ul id="categoryOptions" class="py-1"></ul>
                            </div>
                        </div>
                    </div>
                    <div class="w-full">
                        <label for="defectGroupSearchFilter" class="block text-sm font-medium text-slate-700 mb-1">Defect Group</label>
                        <div class="relative mt-1">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <input type="text" id="defectGroupSearchFilter" name="defectGroupSearchFilter"
                                   placeholder="Search defect groups..."
                                   class="block w-full pl-10 pr-10 py-3 border border-slate-300 rounded-lg shadow-sm focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 sm:text-sm transition-all"
                                   autocomplete="off">
                            <button type="button" id="defectGroupClearBtn" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-150" style="display: none;">
                                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                            <div id="defectGroupDropdown" class="absolute z-20 mt-1 w-full bg-white border border-slate-300 rounded-lg shadow-lg hidden max-h-60 overflow-y-auto">
                                <ul id="defectGroupOptions" class="py-1"></ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4 md:mt-0 flex-shrink-0">
                    <button id="addRootCauseBtn" class="w-full md:w-auto inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg shadow-md text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 transition-all duration-150 transform hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                        Add New Root Cause
                    </button>
                </div>
            </div>
        </div>


        <div id="dataDisplayArea">
            <div class="mb-8 flex flex-wrap items-center justify-between gap-4 p-5 bg-sky-50 rounded-xl shadow-lg">
                <div class="relative flex-grow sm:flex-grow-0 sm:w-auto">
                    <div class="absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-slate-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <input type="text" id="searchInput" placeholder="Search Code or Root Cause..." class="block w-full sm:w-80 pl-12 pr-4 py-3 border border-slate-300 rounded-lg shadow-sm focus:border-cyan-500 sm:text-sm transition-all" />
                </div>
                <div class="flex items-center gap-3">
                    <button id="exportBtn" title="Export Data" class="inline-flex items-center px-4 py-2.5 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 transition-all duration-150 transform hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-slate-500"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" y1="15" x2="12" y2="3"></line></svg>
                        Export
                    </button>
                    <button id="refreshBtn" title="Refresh Data" class="inline-flex items-center px-4 py-2.5 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 transition-all duration-150 transform hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-slate-500"><polyline points="23 4 23 10 17 10"></polyline><polyline points="1 20 1 14 7 14"></polyline><path d="M3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path></svg>
                        Refresh
                    </button>
                </div>
            </div>

            <div class="bg-white shadow-2xl rounded-xl overflow-hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-slate-200">
                        <thead class="bg-slate-100">
                            <tr>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Code <span class="required-asterisk">*</span></th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Root Cause <span class="required-asterisk">*</span></th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Category</th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Defect Group</th>
                                <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">Is Active?</th>
                                <th scope="col" class="px-6 py-4 text-center text-xs font-semibold text-slate-600 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="rootCauseTableBody" class="bg-white divide-y divide-slate-200">
                            </tbody>
                    </table>
                </div>
                <div id="emptyState" class="hidden text-center py-20 px-6">
                    <svg class="mx-auto h-20 w-20 text-slate-300" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                    </svg>
                    <h3 id="emptyStateTitle" class="mt-5 text-xl font-medium text-slate-700">No Root Causes Found</h3>
                    <p id="emptyStateMessage" class="mt-2 text-sm text-slate-500">There are no records matching your current filters, or no data has been added yet.</p>
                    <div id="emptyStateBtnContainer" class="mt-8">
                    <button type="button" id="emptyStateAddBtn" class="inline-flex items-center px-6 py-3 border border-transparent shadow-md text-base font-medium rounded-lg text-white bg-cyan-600 hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 transition-all duration-150 transform hover:scale-105">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" class="-ml-1 mr-2 h-5 w-5"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                        Add New Root Cause
                    </button>
                    </div>
                </div>
            </div>

            <div id="paginationControls" class="mt-10 flex items-center justify-between py-3 px-4 sm:px-0">
                <div class="flex-1 flex justify-between sm:hidden">
                    <button id="prevMobile" class="relative inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors duration-150"> Previous </button>
                    <button id="nextMobile" class="ml-3 relative inline-flex items-center px-4 py-2 border border-slate-300 text-sm font-medium rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors duration-150"> Next </button>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-slate-700">
                            Showing <span id="showingFrom" class="font-medium">1</span> to <span id="showingTo" class="font-medium">10</span> of <span id="totalRecords" class="font-medium">0</span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-lg shadow-sm -space-x-px" aria-label="Pagination">
                            <button id="prevDesktop" class="relative inline-flex items-center px-3 py-2.5 rounded-l-lg border border-slate-300 bg-white text-sm font-medium text-slate-500 hover:bg-slate-50 transition-colors duration-150">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>
                            </button>
                            <span id="pageNumbersContainer" class="flex items-center">
                            </span>
                            <button id="nextDesktop" class="relative inline-flex items-center px-3 py-2.5 rounded-r-lg border border-slate-300 bg-white text-sm font-medium text-slate-500 hover:bg-slate-50 transition-colors duration-150">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div> </div>

    <div id="rootCauseModal" class="fixed z-[60] inset-0 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end sm:items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div id="modalBackdrop" class="fixed inset-0 modal-backdrop transition-opacity duration-300 ease-out" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div id="modalDialog" class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all duration-300 ease-out sm:my-8 sm:align-middle sm:max-w-lg sm:w-full scale-in-center">
                <form id="rootCauseForm">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-cyan-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-cyan-600" id="modalIcon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-xl leading-6 font-semibold text-slate-800" id="modal-title">Add New Root Cause</h3>
                                <input type="hidden" id="modalRecordId" value="">
                                <div class="mt-6 space-y-5">
                                    <div>
                                        <label for="modalCode" class="block text-sm font-medium text-slate-600">Code <span class="required-asterisk">*</span></label>
                                        <input type="text" name="modalCode" id="modalCode" required class="mt-1 block w-full shadow-sm sm:text-sm border-slate-300 rounded-lg p-3 focus:border-cyan-500 transition-all">
                                    </div>
                                    <div>
                                        <div class="flex justify-between items-center">
                                            <label for="modalRootCause" class="block text-sm font-medium text-slate-600">Root Cause <span class="required-asterisk">*</span></label>
                                            <button type="button" id="analyzeRootCauseBtn" class="text-xs text-purple-600 hover:text-purple-800 font-medium inline-flex items-center" title="✨ Analyze and elaborate the root cause description using AI">
                                                ✨ Analyze & Elaborate
                                                <span id="analyzeLoader" class="inline-loader hidden"></span>
                                            </button>
                                        </div>
                                        <textarea id="modalRootCause" name="modalRootCause" rows="3" required class="mt-1 block w-full shadow-sm sm:text-sm border-slate-300 rounded-lg p-3 focus:border-cyan-500 transition-all"></textarea>
                                        <p id="analyzeError" class="text-xs text-red-500 mt-1 hidden"></p>
                                    </div>
                                    <div>
                                        <label for="modalCategory" class="block text-sm font-medium text-slate-600">Category <span class="required-asterisk">*</span></label>
                                        <div class="relative mt-1">
                                            <select id="modalCategory" name="modalCategory" required class="hidden">
                                                <option value="">-- Select Category --</option>
                                            </select>
                                        </div>
                                      </div>
                                      <div>
                                        <label for="modalDefectGroup" class="block text-sm font-medium text-slate-600">Defect Group <span class="required-asterisk">*</span></label>
                                        <div class="relative mt-1">
                                            <select id="modalDefectGroup" name="modalDefectGroup" required class="hidden">
                                                <option value="">-- Select Defect Group --</option>
                                            </select>
                                        </div>
                                      </div>
                                    <div class="flex items-center">
                                        <input id="modalIsActive" name="modalIsActive" type="checkbox" class="h-4 w-4 text-cyan-600 border-slate-300 rounded focus:ring-cyan-500">
                                        <label for="modalIsActive" class="ml-2 block text-sm text-slate-800">Is Active?</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-slate-50 px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse rounded-b-xl">
                        <button type="submit" id="saveModalBtn" class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-6 py-2.5 bg-cyan-600 text-base font-medium text-white hover:bg-cyan-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 sm:ml-3 sm:w-auto sm:text-sm transition-all duration-150 transform hover:scale-105">
                            Save
                        </button>
                        <button type="button" id="cancelModalBtn" class="mt-3 w-full inline-flex justify-center rounded-lg border border-slate-300 shadow-sm px-6 py-2.5 bg-white text-base font-medium text-slate-700 hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 sm:mt-0 sm:w-auto sm:text-sm transition-all duration-150 transform hover:scale-105">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div id="confirmationModal" class="fixed z-[70] inset-0 overflow-y-auto hidden" aria-labelledby="confirmation-modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end sm:items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div id="confirmationModalBackdrop" class="fixed inset-0 modal-backdrop transition-opacity duration-300 ease-out" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div id="confirmationModalDialog" class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all duration-300 ease-out sm:my-8 sm:align-middle sm:max-w-md sm:w-full scale-in-center">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-xl leading-6 font-semibold text-slate-800" id="confirmation-modal-title">Delete Root Cause</h3>
                            <div class="mt-2">
                                <p class="text-sm text-slate-600" id="confirmationModalMessage">
                                    Are you sure you want to delete this root cause? This action cannot be undone.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-slate-50 px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse rounded-b-xl">
                    <button type="button" id="confirmDeleteBtn" class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-5 py-2.5 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm transition-all duration-150 transform hover:scale-105">
                        Delete
                    </button>
                    <button type="button" id="cancelDeleteBtn" class="mt-3 w-full inline-flex justify-center rounded-lg border border-slate-300 shadow-sm px-5 py-2.5 bg-white text-base font-medium text-slate-700 hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 sm:mt-0 sm:w-auto sm:text-sm transition-all duration-150 transform hover:scale-105">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="preventiveActionsModal" class="fixed z-[70] inset-0 overflow-y-auto hidden" aria-labelledby="preventive-actions-modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end sm:items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div id="preventiveActionsModalBackdrop" class="fixed inset-0 modal-backdrop transition-opacity duration-300 ease-out" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div id="preventiveActionsModalDialog" class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all duration-300 ease-out sm:my-8 sm:align-middle sm:max-w-lg sm:w-full scale-in-center">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-sky-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-sky-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9.53 16.122a3 3 0 00-5.78 1.128 2.25 2.25 0 01-2.4 2.245M9.53 16.122A3 3 0 0012 13.489m0 0a3 3 0 002.47 2.633A3.75 3.75 0 0118 19.5M9.53 16.122A3.75 3.75 0 0013.5 19.5m-4.068-4.068A3.75 3.75 0 019.53 16.122m0 0H8.25m4.007 0a3 3 0 002.693-1.589M13.5 19.5V16.622m0-2.844A3.75 3.75 0 0016.5 15M9.53 16.122A3.75 3.75 0 006.5 15m0 0V12.5m0 0A3.75 3.75 0 009.53 9m0 0A3.75 3.75 0 0012.5 12.5m0 0V15" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-xl leading-6 font-semibold text-slate-800" id="preventive-actions-modal-title">✨ Suggested Preventive Actions</h3>
                            <div class="mt-4">
                                <p class="text-sm text-slate-600 mb-1">For Root Cause: <strong id="preventiveActionsContextCode" class="text-slate-700"></strong></p>
                                <p class="text-sm text-slate-500 mb-3" id="preventiveActionsContextDesc"></p>
                                <div id="preventiveActionsLoader" class="loader"></div>
                                <div id="preventiveActionsContent" class="text-sm text-slate-700 space-y-2 prose prose-sm max-w-none">
                                </div>
                                <p id="preventiveActionsError" class="text-sm text-red-600 hidden mt-2"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-slate-50 px-4 py-4 sm:px-6 sm:flex sm:flex-row-reverse rounded-b-xl">
                    <button type="button" id="closePreventiveActionsModalBtn" class="w-full inline-flex justify-center rounded-lg border border-slate-300 shadow-sm px-5 py-2.5 bg-white text-base font-medium text-slate-700 hover:bg-slate-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cyan-500 sm:w-auto sm:text-sm transition-all duration-150 transform hover:scale-105">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="notificationArea" class="fixed bottom-5 right-5 z-[100] w-full max-w-xs sm:max-w-sm space-y-3">
    </div>

<script>
    // --- DOM Elements ---
    const addRootCauseBtn = document.getElementById('addRootCauseBtn');
    const emptyStateAddBtn = document.getElementById('emptyStateAddBtn');

    const rootCauseModal = document.getElementById('rootCauseModal');
    const modalDialog = document.getElementById('modalDialog');
    const modalBackdrop = document.getElementById('modalBackdrop');
    const cancelModalBtn = document.getElementById('cancelModalBtn');
    const rootCauseForm = document.getElementById('rootCauseForm');
    const modalTitle = document.getElementById('modal-title');
    const modalIcon = document.getElementById('modalIcon');
    const modalRecordIdInput = document.getElementById('modalRecordId');
    const modalRootCauseTextarea = document.getElementById('modalRootCause');
    const analyzeRootCauseBtn = document.getElementById('analyzeRootCauseBtn');
    const analyzeLoader = document.getElementById('analyzeLoader');
    const analyzeError = document.getElementById('analyzeError');

    const confirmationModal = document.getElementById('confirmationModal');
    const confirmationModalDialog = document.getElementById('confirmationModalDialog');
    const confirmationModalBackdrop = document.getElementById('confirmationModalBackdrop');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const cancelDeleteBtn = document.getElementById('cancelDeleteBtn');
    const confirmationModalMessage = document.getElementById('confirmationModalMessage');

    const preventiveActionsModal = document.getElementById('preventiveActionsModal');
    const preventiveActionsModalDialog = document.getElementById('preventiveActionsModalDialog');
    const preventiveActionsModalBackdrop = document.getElementById('preventiveActionsModalBackdrop');
    const closePreventiveActionsModalBtn = document.getElementById('closePreventiveActionsModalBtn');
    const preventiveActionsContextCode = document.getElementById('preventiveActionsContextCode');
    const preventiveActionsContextDesc = document.getElementById('preventiveActionsContextDesc');
    const preventiveActionsLoader = document.getElementById('preventiveActionsLoader');
    const preventiveActionsContent = document.getElementById('preventiveActionsContent');
    const preventiveActionsError = document.getElementById('preventiveActionsError');

    const rootCauseTableBody = document.getElementById('rootCauseTableBody');
    const searchInput = document.getElementById('searchInput');
    const refreshBtn = document.getElementById('refreshBtn');
    const exportBtn = document.getElementById('exportBtn');
    const notificationArea = document.getElementById('notificationArea');

    const emptyState = document.getElementById('emptyState');
    const emptyStateTitle = document.getElementById('emptyStateTitle');
    const emptyStateMessage = document.getElementById('emptyStateMessage');
    const emptyStateBtnContainer = document.getElementById('emptyStateBtnContainer');

    const paginationControls = document.getElementById('paginationControls');
    const showingFromElem = document.getElementById('showingFrom');
    const showingToElem = document.getElementById('showingTo');
    const totalRecordsElem = document.getElementById('totalRecords');
    const prevMobileBtn = document.getElementById('prevMobile');
    const nextMobileBtn = document.getElementById('nextMobile');
    const prevDesktopBtn = document.getElementById('prevDesktop');
    const nextDesktopBtn = document.getElementById('nextDesktop');
    const pageNumbersContainer = document.getElementById('pageNumbersContainer');

    const dataDisplayArea = document.getElementById('dataDisplayArea');

    // Search filter elements
    const categorySearchFilter = document.getElementById('categorySearchFilter');
    const defectGroupSearchFilter = document.getElementById('defectGroupSearchFilter');
    const categoryClearBtn = document.getElementById('categoryClearBtn');
    const defectGroupClearBtn = document.getElementById('defectGroupClearBtn');
    const categoryDropdown = document.getElementById('categoryDropdown');
    const defectGroupDropdown = document.getElementById('defectGroupDropdown');
    const categoryOptions = document.getElementById('categoryOptions');
    const defectGroupOptions = document.getElementById('defectGroupOptions');

    // Modal dropdown elements
    const modalCategorySelect = document.getElementById('modalCategory');
    const modalDefectGroupSelect = document.getElementById('modalDefectGroup');

    // State for search filters
    let selectedCategory = '';
    let selectedDefectGroup = '';
    let categoryData = [];
    let defectGroupData = [];

    // Overall Summary Card Elements
    const overallTotalCategoriesElem = document.getElementById('overallTotalCategories');
    const overallTotalDefectGroupsElem = document.getElementById('overallTotalDefectGroups');
    const overallActiveCausesElem = document.getElementById('overallActiveCauses');
    const overallInactiveCausesElem = document.getElementById('overallInactiveCauses');


    let rootCausesData = [
        { id: 1, code: 'HW-001', rootCause: 'Loose connection on motherboard', category: 'Component Failure', defectGroup: 'Electrical', isActive: true },
        { id: 2, code: 'SW-001', rootCause: 'Null pointer exception in user module', category: 'Software Bug', defectGroup: 'Logical', isActive: true },
        { id: 3, code: 'ASM-001', rootCause: 'Incorrect torque applied to screw A1', category: 'Assembly Faults', defectGroup: 'Mechanical', isActive: false },
        { id: 4, code: 'PROC-001', rootCause: 'Outdated SOP for testing phase', category: 'Process Issue', defectGroup: 'Human Error', isActive: true },
        { id: 5, code: 'HW-002', rootCause: 'Overheating of CPU due to fan malfunction', category: 'Component Failure', defectGroup: 'Mechanical', isActive: true },
        { id: 6, code: 'SW-002', rootCause: 'Memory leak in data processing service', category: 'Software Bug', defectGroup: 'Logical', isActive: true },
        { id: 7, code: 'ASM-002', rootCause: 'Wrong component used in sub-assembly X', category: 'Assembly Faults', defectGroup: 'Human Error', isActive: true },
        { id: 8, code: 'PROC-002', rootCause: 'Insufficient training for new operators', category: 'Process Issue', defectGroup: 'Human Error', isActive: false },
        { id: 9, code: 'HW-003', rootCause: 'Capacitor C22 failed on PCB rev 1.2', category: 'Component Failure', defectGroup: 'Electrical', isActive: true },
        { id: 10, code: 'SW-003', rootCause: 'Race condition during database write', category: 'Software Bug', defectGroup: 'Logical', isActive: false },
        { id: 11, code: 'ASM-003', rootCause: 'Solder joint crack on U5', category: 'Assembly Faults', defectGroup: 'Mechanical', isActive: true },
    ];
    let currentFilteredDataForTable = [];
    let currentEditId = null;
    let recordToDeleteId = null;
    let currentPage = 1;
    const recordsPerPage = 5;
    const GEMINI_API_KEY = "";
    let isInlineEditing = false;
    let inlineEditingRowId = null;

    function populateSelectWithOptions(selectElement, optionsArray, includePlaceholder = true, placeholderText = "-- Select --") {
        selectElement.innerHTML = '';
        if (includePlaceholder) {
            const placeholder = document.createElement('option');
            placeholder.value = "";
            placeholder.textContent = placeholderText;
            selectElement.appendChild(placeholder);
        }
        optionsArray.forEach(opt => {
            const option = document.createElement('option');
            option.value = opt;
            option.textContent = opt;
            selectElement.appendChild(option);
        });
    }

    function updateFilterDropdownOptions() {
        const uniqueCategories = [...new Set(rootCausesData.map(item => item.category))].sort();
        const uniqueDefectGroups = [...new Set(rootCausesData.map(item => item.defectGroup))].sort();

        // Update data arrays for search functionality
        categoryData = uniqueCategories;
        defectGroupData = uniqueDefectGroups;

        // Update modal dropdowns
        populateSelectWithOptions(modalCategorySelect, uniqueCategories, true, "-- Select Category --");
        populateSelectWithOptions(modalDefectGroupSelect, uniqueDefectGroups, true, "-- Select Defect Group --");

        const modalCatCustom = modalCategorySelect.closest('.custom-dropdown');
        if (modalCatCustom) modalCatCustom.refreshCustomDropdown();

        const modalDefectCustom = modalDefectGroupSelect.closest('.custom-dropdown');
        if (modalDefectCustom) modalDefectCustom.refreshCustomDropdown();
    }

    // Search filter functionality
    function initializeSearchFilters() {
        // Category search filter
        categorySearchFilter.addEventListener('input', (e) => {
            const searchTerm = e.target.value;
            showCategoryDropdown(searchTerm);
            categoryClearBtn.style.display = searchTerm ? 'flex' : 'none';
        });

        categorySearchFilter.addEventListener('focus', () => {
            showCategoryDropdown(categorySearchFilter.value);
        });

        categoryClearBtn.addEventListener('click', () => {
            categorySearchFilter.value = '';
            selectedCategory = '';
            categoryClearBtn.style.display = 'none';
            categoryDropdown.classList.add('hidden');
            renderTable();
        });

        // Defect Group search filter
        defectGroupSearchFilter.addEventListener('input', (e) => {
            const searchTerm = e.target.value;
            showDefectGroupDropdown(searchTerm);
            defectGroupClearBtn.style.display = searchTerm ? 'flex' : 'none';
        });

        defectGroupSearchFilter.addEventListener('focus', () => {
            showDefectGroupDropdown(defectGroupSearchFilter.value);
        });

        defectGroupClearBtn.addEventListener('click', () => {
            defectGroupSearchFilter.value = '';
            selectedDefectGroup = '';
            defectGroupClearBtn.style.display = 'none';
            defectGroupDropdown.classList.add('hidden');
            renderTable();
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!categorySearchFilter.contains(e.target) && !categoryDropdown.contains(e.target)) {
                categoryDropdown.classList.add('hidden');
            }
            if (!defectGroupSearchFilter.contains(e.target) && !defectGroupDropdown.contains(e.target)) {
                defectGroupDropdown.classList.add('hidden');
            }
        });

        // Keyboard navigation
        categorySearchFilter.addEventListener('keydown', (e) => {
            handleDropdownKeyboard(e, categoryOptions, categoryDropdown, (value) => {
                categorySearchFilter.value = value;
                selectedCategory = value;
                categoryClearBtn.style.display = value ? 'flex' : 'none';
                categoryDropdown.classList.add('hidden');
                renderTable();
            });
        });

        defectGroupSearchFilter.addEventListener('keydown', (e) => {
            handleDropdownKeyboard(e, defectGroupOptions, defectGroupDropdown, (value) => {
                defectGroupSearchFilter.value = value;
                selectedDefectGroup = value;
                defectGroupClearBtn.style.display = value ? 'flex' : 'none';
                defectGroupDropdown.classList.add('hidden');
                renderTable();
            });
        });
    }

    function showCategoryDropdown(searchTerm = '') {
        const filteredCategories = categoryData.filter(category =>
            category.toLowerCase().includes(searchTerm.toLowerCase())
        );

        categoryOptions.innerHTML = '';

        // Add "All Categories" option
        if (!searchTerm || 'all categories'.includes(searchTerm.toLowerCase())) {
            const allOption = document.createElement('li');
            allOption.className = 'px-3 py-2 text-sm hover:bg-cyan-50 cursor-pointer transition-colors duration-150';
            allOption.textContent = 'All Categories';
            allOption.addEventListener('click', () => {
                categorySearchFilter.value = '';
                selectedCategory = '';
                categoryClearBtn.style.display = 'none';
                categoryDropdown.classList.add('hidden');
                renderTable();
            });
            categoryOptions.appendChild(allOption);
        }

        filteredCategories.forEach(category => {
            const option = document.createElement('li');
            option.className = 'px-3 py-2 text-sm hover:bg-cyan-50 cursor-pointer transition-colors duration-150';

            // Highlight matching text
            if (searchTerm) {
                const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                option.innerHTML = category.replace(regex, '<mark class="bg-yellow-200 text-slate-900 px-0.5 rounded">$1</mark>');
            } else {
                option.textContent = category;
            }

            option.addEventListener('click', () => {
                categorySearchFilter.value = category;
                selectedCategory = category;
                categoryClearBtn.style.display = 'flex';
                categoryDropdown.classList.add('hidden');
                renderTable();
            });
            categoryOptions.appendChild(option);
        });

        if (filteredCategories.length === 0 && searchTerm) {
            const noResults = document.createElement('li');
            noResults.className = 'px-3 py-2 text-sm text-gray-500 text-center italic';
            noResults.textContent = `No categories found for "${searchTerm}"`;
            categoryOptions.appendChild(noResults);
        }

        categoryDropdown.classList.remove('hidden');
    }

    function showDefectGroupDropdown(searchTerm = '') {
        const filteredDefectGroups = defectGroupData.filter(group =>
            group.toLowerCase().includes(searchTerm.toLowerCase())
        );

        defectGroupOptions.innerHTML = '';

        // Add "All Defect Groups" option
        if (!searchTerm || 'all defect groups'.includes(searchTerm.toLowerCase())) {
            const allOption = document.createElement('li');
            allOption.className = 'px-3 py-2 text-sm hover:bg-cyan-50 cursor-pointer transition-colors duration-150';
            allOption.textContent = 'All Defect Groups';
            allOption.addEventListener('click', () => {
                defectGroupSearchFilter.value = '';
                selectedDefectGroup = '';
                defectGroupClearBtn.style.display = 'none';
                defectGroupDropdown.classList.add('hidden');
                renderTable();
            });
            defectGroupOptions.appendChild(allOption);
        }

        filteredDefectGroups.forEach(group => {
            const option = document.createElement('li');
            option.className = 'px-3 py-2 text-sm hover:bg-cyan-50 cursor-pointer transition-colors duration-150';

            // Highlight matching text
            if (searchTerm) {
                const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                option.innerHTML = group.replace(regex, '<mark class="bg-yellow-200 text-slate-900 px-0.5 rounded">$1</mark>');
            } else {
                option.textContent = group;
            }

            option.addEventListener('click', () => {
                defectGroupSearchFilter.value = group;
                selectedDefectGroup = group;
                defectGroupClearBtn.style.display = 'flex';
                defectGroupDropdown.classList.add('hidden');
                renderTable();
            });
            defectGroupOptions.appendChild(option);
        });

        if (filteredDefectGroups.length === 0 && searchTerm) {
            const noResults = document.createElement('li');
            noResults.className = 'px-3 py-2 text-sm text-gray-500 text-center italic';
            noResults.textContent = `No defect groups found for "${searchTerm}"`;
            defectGroupOptions.appendChild(noResults);
        }

        defectGroupDropdown.classList.remove('hidden');
    }

    function handleDropdownKeyboard(e, optionsContainer, dropdown, selectCallback) {
        const options = optionsContainer.querySelectorAll('li:not(.text-gray-500)');
        let currentIndex = Array.from(options).findIndex(opt => opt.classList.contains('bg-cyan-100'));

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (currentIndex < options.length - 1) {
                if (currentIndex >= 0) options[currentIndex].classList.remove('bg-cyan-100');
                currentIndex++;
                options[currentIndex].classList.add('bg-cyan-100');
                options[currentIndex].scrollIntoView({ block: 'nearest' });
            }
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            if (currentIndex > 0) {
                options[currentIndex].classList.remove('bg-cyan-100');
                currentIndex--;
                options[currentIndex].classList.add('bg-cyan-100');
                options[currentIndex].scrollIntoView({ block: 'nearest' });
            }
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (currentIndex >= 0 && options[currentIndex]) {
                const selectedText = options[currentIndex].textContent;
                selectCallback(selectedText === 'All Categories' || selectedText === 'All Defect Groups' ? '' : selectedText);
            }
        } else if (e.key === 'Escape') {
            e.preventDefault();
            dropdown.classList.add('hidden');
        }
    }

    function initializeCustomDropdown(selectElementId) {
        const originalSelect = document.getElementById(selectElementId);
        if (!originalSelect) return;

        const existingCustomDropdown = originalSelect.parentNode.querySelector('.custom-dropdown');
        if (existingCustomDropdown) existingCustomDropdown.remove();

        originalSelect.classList.add('hidden');

        const container = document.createElement('div');
        container.className = 'custom-dropdown relative';
        originalSelect.parentNode.insertBefore(container, originalSelect);

        const toggleButton = document.createElement('button');
        toggleButton.type = 'button';
        toggleButton.className = 'custom-dropdown-toggle w-full flex items-center justify-between text-left py-3 px-4 border border-slate-300 bg-white rounded-lg shadow-sm sm:text-sm transition-all';
        toggleButton.setAttribute('aria-haspopup', 'listbox');
        toggleButton.setAttribute('aria-expanded', 'false');

        const selectedTextSpan = document.createElement('span');
        selectedTextSpan.className = 'custom-dropdown-selected-text truncate';

        const arrowIcon = document.createElement('span');
        arrowIcon.innerHTML = `<svg class="w-5 h-5 text-slate-400 transform transition-transform duration-200" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>`;

        toggleButton.appendChild(selectedTextSpan);
        toggleButton.appendChild(arrowIcon);
        container.appendChild(toggleButton);

        const dropdownPanel = document.createElement('div');
        dropdownPanel.className = 'custom-dropdown-panel hidden absolute z-20 mt-1 w-full bg-white border border-slate-300 rounded-lg shadow-lg';
        container.appendChild(dropdownPanel);

        const searchContainer = document.createElement('div');
        searchContainer.className = 'relative border-b border-slate-200';

        const resultsCounter = document.createElement('div');
        resultsCounter.className = 'absolute top-0 right-0 mt-1 mr-2 text-xs text-gray-500 bg-white px-1 rounded hidden';
        resultsCounter.id = `results-counter-${selectElementId}`;

        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'custom-dropdown-search-input w-full py-2.5 pl-10 pr-8 border-0 sm:text-sm focus:outline-none focus:ring-0 bg-gray-50';
        searchInput.placeholder = 'Type to search...';

        const searchIcon = document.createElement('div');
        searchIcon.className = 'absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none';
        searchIcon.innerHTML = `<svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
        </svg>`;

        const clearButton = document.createElement('button');
        clearButton.type = 'button';
        clearButton.className = 'absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-150';
        clearButton.innerHTML = `<svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>`;
        clearButton.style.display = 'none';

        clearButton.addEventListener('click', (e) => {
            e.stopPropagation();
            searchInput.value = '';
            populateOptions('');
            clearButton.style.display = 'none';
            searchInput.focus();
        });

        searchContainer.appendChild(searchIcon);
        searchContainer.appendChild(searchInput);
        searchContainer.appendChild(clearButton);
        searchContainer.appendChild(resultsCounter);
        dropdownPanel.appendChild(searchContainer);

        const optionsList = document.createElement('ul');
        optionsList.className = 'custom-dropdown-options-list max-h-60 overflow-y-auto py-1';
        optionsList.setAttribute('role', 'listbox');
        dropdownPanel.appendChild(optionsList);

        let currentOptions = [];

        function populateOptions(filter = '') {
            optionsList.innerHTML = '';
            currentOptions = [];
            let matchCount = 0;

            Array.from(originalSelect.options).forEach((option) => {
                const optionText = option.text.toLowerCase();
                const filterText = filter.toLowerCase();

                if (optionText.includes(filterText)) {
                    const listItem = document.createElement('li');
                    listItem.className = 'custom-dropdown-option text-slate-700 px-3 py-2.5 text-sm hover:bg-cyan-50 cursor-pointer transition-colors duration-150';

                    // Highlight matching text
                    if (filter && option.value !== '') {
                        const regex = new RegExp(`(${filter.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
                        const highlightedText = option.text.replace(regex, '<mark class="bg-yellow-200 text-slate-900 px-0.5 rounded">$1</mark>');
                        listItem.innerHTML = highlightedText;
                    } else {
                        listItem.textContent = option.text;
                    }

                    listItem.dataset.value = option.value;
                    listItem.setAttribute('role', 'option');
                    listItem.setAttribute('aria-selected', option.selected ? 'true' : 'false');

                    if (option.selected) {
                        listItem.classList.add('font-semibold', 'bg-cyan-100', 'text-cyan-900');
                    }

                    listItem.addEventListener('click', () => {
                        originalSelect.value = option.value;
                        updateSelectedText();
                        const event = new Event('change', { bubbles: true });
                        originalSelect.dispatchEvent(event);
                        closeDropdown();
                        searchInput.value = ''; // Clear search after selection
                    });

                    optionsList.appendChild(listItem);
                    currentOptions.push(listItem);
                    matchCount++;
                }
            });

            if (matchCount === 0) {
                const noResultsItem = document.createElement('li');
                noResultsItem.className = 'text-slate-500 px-3 py-2.5 text-sm text-center italic';
                noResultsItem.innerHTML = `
                    <div class="flex items-center justify-center space-x-2">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <span>No results found for "${filter}"</span>
                    </div>
                `;
                optionsList.appendChild(noResultsItem);
                resultsCounter.classList.add('hidden');
            } else if (filter && matchCount > 0) {
                // Show results counter when searching
                resultsCounter.textContent = `${matchCount} result${matchCount !== 1 ? 's' : ''}`;
                resultsCounter.classList.remove('hidden');
            } else {
                resultsCounter.classList.add('hidden');
            }
        }

        function updateSelectedText() {
            const selectedOption = originalSelect.options[originalSelect.selectedIndex];
            if (selectedOption) {
                selectedTextSpan.textContent = selectedOption.text;
                selectedTextSpan.classList.toggle('text-slate-500', selectedOption.value === "");
            } else if (originalSelect.options.length > 0 && originalSelect.options[0].value === "") {
                 selectedTextSpan.textContent = originalSelect.options[0].text;
                 selectedTextSpan.classList.add('text-slate-500');
            } else {
                selectedTextSpan.textContent = "Select an option";
                selectedTextSpan.classList.add('text-slate-500');
            }
        }

        let highlightedIndex = -1;

        function handleKeyDown(e) {
            if (dropdownPanel.classList.contains('hidden')) return;
            if (currentOptions.length === 0 && e.key !== 'Escape') return;

            if (e.key === 'ArrowDown') {
                e.preventDefault();
                highlightedIndex = (highlightedIndex + 1) % currentOptions.length;
                updateHighlight();
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                highlightedIndex = (highlightedIndex - 1 + currentOptions.length) % currentOptions.length;
                updateHighlight();
            } else if (e.key === 'Enter' && highlightedIndex >= 0 && currentOptions[highlightedIndex]) {
                e.preventDefault();
                currentOptions[highlightedIndex].click();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                closeDropdown();
            }
        }

        function updateHighlight() {
            currentOptions.forEach((opt, idx) => {
                opt.classList.toggle('highlighted', idx === highlightedIndex);
                if (idx === highlightedIndex) {
                    opt.scrollIntoView({ block: 'nearest' });
                }
            });
        }

        function openDropdown() {
            // Clear search input when opening
            searchInput.value = '';
            populateOptions('');
            dropdownPanel.classList.remove('hidden');
            toggleButton.setAttribute('aria-expanded', 'true');
            arrowIcon.querySelector('svg').classList.add('rotate-180');

            // Focus on search input with a slight delay to ensure dropdown is visible
            setTimeout(() => {
                searchInput.focus();
                searchInput.select(); // Select any existing text
            }, 50);

            highlightedIndex = currentOptions.findIndex(opt => opt.dataset.value === originalSelect.value);
            if (highlightedIndex === -1 && currentOptions.length > 0) highlightedIndex = 0;
            updateHighlight();
            document.addEventListener('click', handleClickOutside, true);
            container.addEventListener('keydown', handleKeyDown);
        }

        function closeDropdown() {
            dropdownPanel.classList.add('hidden');
            toggleButton.setAttribute('aria-expanded', 'false');
            arrowIcon.querySelector('svg').classList.remove('rotate-180');
            highlightedIndex = -1;
            document.removeEventListener('click', handleClickOutside, true);
            container.removeEventListener('keydown', handleKeyDown);
        }

        toggleButton.addEventListener('click', (e) => {
            e.stopPropagation();
            if (dropdownPanel.classList.contains('hidden')) {
                openDropdown();
            } else {
                closeDropdown();
            }
        });

        searchInput.addEventListener('input', () => {
            const searchValue = searchInput.value;
            populateOptions(searchValue);
            highlightedIndex = currentOptions.length > 0 ? 0 : -1;
            updateHighlight();

            // Show/hide clear button based on input content
            clearButton.style.display = searchValue.length > 0 ? 'flex' : 'none';
        });

        searchInput.addEventListener('click', (e) => e.stopPropagation());

        // Add additional keyboard shortcuts for search input
        searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && highlightedIndex >= 0 && currentOptions[highlightedIndex]) {
                e.preventDefault();
                currentOptions[highlightedIndex].click();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                if (searchInput.value) {
                    searchInput.value = '';
                    populateOptions('');
                    clearButton.style.display = 'none';
                } else {
                    closeDropdown();
                }
            }
        });

        function handleClickOutside(event) {
            if (!container.contains(event.target)) {
                closeDropdown();
            }
        }

        updateSelectedText();
        originalSelect.addEventListener('change', updateSelectedText);

        container.refreshCustomDropdown = (newOptionsArray, includePlaceholder = true, placeholderText = "-- Select --") => {
            if (newOptionsArray) {
                populateSelectWithOptions(originalSelect, newOptionsArray, includePlaceholder, placeholderText);
            }
            updateSelectedText();
            if (!dropdownPanel.classList.contains('hidden')) {
                populateOptions(searchInput.value);
            }
        };
        return container;
    }

    function displayOverallSummary() {
        const uniqueCategories = [...new Set(rootCausesData.map(item => item.category))];
        overallTotalCategoriesElem.textContent = uniqueCategories.length;

        const uniqueDefectGroups = [...new Set(rootCausesData.map(item => item.defectGroup))];
        overallTotalDefectGroupsElem.textContent = uniqueDefectGroups.length;

        overallActiveCausesElem.textContent = rootCausesData.filter(item => item.isActive).length;
        overallInactiveCausesElem.textContent = rootCausesData.filter(item => !item.isActive).length;
    }

    function addInlineEditRow() {
        if (isInlineEditing) {
            showNotification('Please save or cancel the current edit first.', 'error');
            return;
        }

        isInlineEditing = true;
        inlineEditingRowId = 'new';

        // Create a temporary new row at the top of the table
        const newRow = rootCauseTableBody.insertRow(0);
        newRow.className = 'bg-yellow-50 border-2 border-yellow-300 hover:bg-yellow-100 transition-colors duration-150';
        newRow.id = 'inline-edit-row';

        const uniqueCategories = [...new Set(rootCausesData.map(item => item.category))].sort();
        const uniqueDefectGroups = [...new Set(rootCausesData.map(item => item.defectGroup))].sort();

        newRow.innerHTML = `
            <td class="px-6 py-4">
                <input type="text" id="inline-code" placeholder="Enter code..."
                       class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500"
                       required>
            </td>
            <td class="px-6 py-4">
                <textarea id="inline-rootcause" placeholder="Enter root cause..." rows="2"
                          class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 resize-none"
                          required></textarea>
            </td>
            <td class="px-6 py-4">
                <select id="inline-category" class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500" required>
                    <option value="">Select Category</option>
                    ${uniqueCategories.map(cat => `<option value="${cat}">${cat}</option>`).join('')}
                </select>
            </td>
            <td class="px-6 py-4">
                <select id="inline-defectgroup" class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500" required>
                    <option value="">Select Defect Group</option>
                    ${uniqueDefectGroups.map(group => `<option value="${group}">${group}</option>`).join('')}
                </select>
            </td>
            <td class="px-6 py-4">
                <label class="inline-flex items-center">
                    <input type="checkbox" id="inline-isactive" checked
                           class="h-4 w-4 text-cyan-600 border-slate-300 rounded focus:ring-cyan-500">
                    <span class="ml-2 text-sm text-slate-700">Active</span>
                </label>
            </td>
            <td class="px-6 py-4 text-center space-x-2">
                <button class="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors duration-150"
                        onclick="saveInlineEdit()" title="Save">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                        <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    Save
                </button>
                <button class="inline-flex items-center px-3 py-1.5 bg-gray-600 text-white text-sm rounded-lg hover:bg-gray-700 transition-colors duration-150"
                        onclick="cancelInlineEdit()" title="Cancel">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                    Cancel
                </button>
            </td>
        `;

        // Focus on the first input
        document.getElementById('inline-code').focus();

        // Pre-populate with filter values if any
        if (selectedCategory) {
            document.getElementById('inline-category').value = selectedCategory;
        }
        if (selectedDefectGroup) {
            document.getElementById('inline-defectgroup').value = selectedDefectGroup;
        }

        // Add keyboard event listeners for inline editing
        addInlineEditKeyboardListeners();
    }

    function saveInlineEdit() {
        const code = document.getElementById('inline-code').value.trim();
        const rootCause = document.getElementById('inline-rootcause').value.trim();
        const category = document.getElementById('inline-category').value;
        const defectGroup = document.getElementById('inline-defectgroup').value;
        const isActive = document.getElementById('inline-isactive').checked;

        // Validation
        if (!code || !rootCause || !category || !defectGroup) {
            showNotification('Please fill in all required fields.', 'error');
            return;
        }

        // Check for duplicate code
        if (rootCausesData.some(item => item.code.toLowerCase() === code.toLowerCase())) {
            showNotification('Code already exists. Please use a unique code.', 'error');
            return;
        }

        // Create new record
        const newId = rootCausesData.length > 0 ? Math.max(...rootCausesData.map(rc => rc.id)) + 1 : 1;
        const newRecord = { id: newId, code, rootCause, category, defectGroup, isActive };

        // Add to data
        rootCausesData.push(newRecord);

        // Reset inline editing state
        isInlineEditing = false;
        inlineEditingRowId = null;

        // Update UI
        updateFilterDropdownOptions();
        displayOverallSummary();
        renderTable();

        showNotification('Root cause added successfully!', 'success');
    }

    function cancelInlineEdit() {
        isInlineEditing = false;
        inlineEditingRowId = null;

        // Remove the inline edit row
        const editRow = document.getElementById('inline-edit-row');
        if (editRow) {
            editRow.remove();
        }

        showNotification('Edit cancelled.', 'info');
    }

    function addInlineEditRowWithData(data) {
        const newRow = rootCauseTableBody.insertRow(0);
        newRow.className = 'bg-yellow-50 border-2 border-yellow-300 hover:bg-yellow-100 transition-colors duration-150';
        newRow.id = 'inline-edit-row';

        const uniqueCategories = [...new Set(rootCausesData.map(item => item.category))].sort();
        const uniqueDefectGroups = [...new Set(rootCausesData.map(item => item.defectGroup))].sort();

        newRow.innerHTML = `
            <td class="px-6 py-4">
                <input type="text" id="inline-code" placeholder="Enter code..." value="${data.code}"
                       class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500"
                       required>
            </td>
            <td class="px-6 py-4">
                <textarea id="inline-rootcause" placeholder="Enter root cause..." rows="2"
                          class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500 resize-none"
                          required>${data.rootCause}</textarea>
            </td>
            <td class="px-6 py-4">
                <select id="inline-category" class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500" required>
                    <option value="">Select Category</option>
                    ${uniqueCategories.map(cat => `<option value="${cat}" ${cat === data.category ? 'selected' : ''}>${cat}</option>`).join('')}
                </select>
            </td>
            <td class="px-6 py-4">
                <select id="inline-defectgroup" class="w-full px-3 py-2 border border-slate-300 rounded-lg text-sm focus:border-cyan-500 focus:ring-1 focus:ring-cyan-500" required>
                    <option value="">Select Defect Group</option>
                    ${uniqueDefectGroups.map(group => `<option value="${group}" ${group === data.defectGroup ? 'selected' : ''}>${group}</option>`).join('')}
                </select>
            </td>
            <td class="px-6 py-4">
                <label class="inline-flex items-center">
                    <input type="checkbox" id="inline-isactive" ${data.isActive ? 'checked' : ''}
                           class="h-4 w-4 text-cyan-600 border-slate-300 rounded focus:ring-cyan-500">
                    <span class="ml-2 text-sm text-slate-700">Active</span>
                </label>
            </td>
            <td class="px-6 py-4 text-center space-x-2">
                <button class="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 transition-colors duration-150"
                        onclick="saveInlineEdit()" title="Save">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                        <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    Save
                </button>
                <button class="inline-flex items-center px-3 py-1.5 bg-gray-600 text-white text-sm rounded-lg hover:bg-gray-700 transition-colors duration-150"
                        onclick="cancelInlineEdit()" title="Cancel">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                    Cancel
                </button>
            </td>
        `;

        // Add keyboard event listeners for inline editing
        addInlineEditKeyboardListeners();
    }

    function addInlineEditKeyboardListeners() {
        const inlineInputs = ['inline-code', 'inline-rootcause', 'inline-category', 'inline-defectgroup', 'inline-isactive'];

        inlineInputs.forEach(inputId => {
            const element = document.getElementById(inputId);
            if (element) {
                element.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        saveInlineEdit();
                    } else if (e.key === 'Escape') {
                        e.preventDefault();
                        cancelInlineEdit();
                    }
                });
            }
        });
    }


    function showNotification(message, type = 'success') {
        const bgColor = type === 'success' ? 'bg-green-500' : (type === 'error' ? 'bg-red-500' : 'bg-sky-500');
        const iconSVG = type === 'success' ?
            `<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>` :
            type === 'error' ?
            `<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>` :
            `<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>`;

        const notification = document.createElement('div');
        notification.className = `p-4 rounded-lg shadow-xl text-white ${bgColor} flex items-center space-x-3 transition-all duration-300 ease-out transform translate-x-full opacity-0 animate-slideIn`;
        notification.innerHTML = `
            ${iconSVG}
            <span>${message}</span>
            <button class="ml-auto text-white hover:text-gray-200 opacity-75 hover:opacity-100">&times;</button>
        `;

        const style = document.createElement('style');
        style.innerHTML = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .animate-slideIn {
                animation: slideIn 0.3s ease-out forwards;
            }
        `;
        document.head.appendChild(style);

        notification.querySelector('button').onclick = () => {
            notification.classList.remove('animate-slideIn');
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                notification.remove();
                if (document.head.contains(style)) document.head.removeChild(style);
            }, 300);
        };
        notificationArea.appendChild(notification);

        setTimeout(() => {
            if (notification.parentElement) {
                 notification.querySelector('button').click();
            }
        }, 5000);
    }

    function openModal(mode = 'add', record = null) {
        currentEditId = mode === 'edit' && record ? record.id : null;
        modalTitle.textContent = mode === 'add' ? 'Add New Root Cause' : 'Edit Root Cause';
        modalIcon.innerHTML = mode === 'add' ?
            `<path stroke-linecap="round" stroke-linejoin="round" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />` :
            `<path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10" />`;

        rootCauseForm.reset();
        modalRecordIdInput.value = record ? record.id : '';
        analyzeError.classList.add('hidden');

        if (mode === 'edit' && record) {
            document.getElementById('modalCode').value = record.code;
            modalRootCauseTextarea.value = record.rootCause;
            modalCategorySelect.value = record.category;
            modalDefectGroupSelect.value = record.defectGroup;
            document.getElementById('modalIsActive').checked = record.isActive;
        } else {
            modalRootCauseTextarea.value = '';
            modalCategorySelect.value = selectedCategory || "";
            modalDefectGroupSelect.value = selectedDefectGroup || "";
            document.getElementById('modalIsActive').checked = true;
        }

        modalCategorySelect.closest('.custom-dropdown').refreshCustomDropdown();
        modalDefectGroupSelect.closest('.custom-dropdown').refreshCustomDropdown();

        rootCauseModal.classList.remove('hidden');
        modalBackdrop.classList.remove('hidden');
        modalDialog.classList.remove('scale-out-center');
        modalDialog.classList.add('scale-in-center');
        setTimeout(() => modalBackdrop.classList.add('opacity-100'), 10);
    }

    function closeModal() {
        modalDialog.classList.remove('scale-in-center');
        modalDialog.classList.add('scale-out-center');
        modalBackdrop.classList.remove('opacity-100');
        setTimeout(() => {
            rootCauseModal.classList.add('hidden');
            modalBackdrop.classList.add('hidden');
            modalDialog.classList.remove('scale-out-center');
        }, 300);
        currentEditId = null;
        rootCauseForm.reset();
        analyzeError.classList.add('hidden');
        modalCategorySelect.closest('.custom-dropdown').refreshCustomDropdown();
        modalDefectGroupSelect.closest('.custom-dropdown').refreshCustomDropdown();
    }

    function openConfirmationModal(recordId, recordCode) {
        recordToDeleteId = recordId;
        confirmationModalMessage.innerHTML = `Are you sure you want to delete root cause <strong>"${recordCode}"</strong>? This action cannot be undone.`;
        confirmationModal.classList.remove('hidden');
        confirmationModalBackdrop.classList.remove('hidden');
        confirmationModalDialog.classList.remove('scale-out-center');
        confirmationModalDialog.classList.add('scale-in-center');
        setTimeout(() => confirmationModalBackdrop.classList.add('opacity-100'), 10);
    }

    function closeConfirmationModal() {
        confirmationModalDialog.classList.remove('scale-in-center');
        confirmationModalDialog.classList.add('scale-out-center');
        confirmationModalBackdrop.classList.remove('opacity-100');
        setTimeout(() => {
            confirmationModal.classList.add('hidden');
            confirmationModalBackdrop.classList.add('hidden');
            confirmationModalDialog.classList.remove('scale-out-center');
        }, 300);
        recordToDeleteId = null;
    }

    function openPreventiveActionsModal(record) {
        preventiveActionsContextCode.textContent = record.code;
        preventiveActionsContextDesc.textContent = record.rootCause;
        preventiveActionsContent.innerHTML = '';
        preventiveActionsError.classList.add('hidden');
        preventiveActionsError.textContent = '';
        preventiveActionsLoader.classList.remove('hidden');

        preventiveActionsModal.classList.remove('hidden');
        preventiveActionsModalBackdrop.classList.remove('hidden');
        preventiveActionsModalDialog.classList.remove('scale-out-center');
        preventiveActionsModalDialog.classList.add('scale-in-center');
        setTimeout(() => preventiveActionsModalBackdrop.classList.add('opacity-100'), 10);

        fetchPreventiveActions(record);
    }

    function closePreventiveActionsModal() {
        preventiveActionsModalDialog.classList.remove('scale-in-center');
        preventiveActionsModalDialog.classList.add('scale-out-center');
        preventiveActionsModalBackdrop.classList.remove('opacity-100');
        setTimeout(() => {
            preventiveActionsModal.classList.add('hidden');
            preventiveActionsModalBackdrop.classList.add('hidden');
            preventiveActionsModalDialog.classList.remove('scale-out-center');
        }, 300);
    }

    async function fetchPreventiveActions(record) {
        const prompt = `Given a root cause in the category '${record.category}' and defect group '${record.defectGroup}' described as: '${record.rootCause}', suggest 3-5 concise preventive actions or mitigation strategies. Present them as a numbered list (e.g., 1. Action one. 2. Action two.). Each action should be a complete sentence.`;
        const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;
        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API request failed: ${errorData?.error?.message || response.status}`);
            }
            const result = await response.json();
            preventiveActionsLoader.classList.add('hidden');
            if (result.candidates && result.candidates[0]?.content?.parts[0]?.text) {
                let text = result.candidates[0].content.parts[0].text;
                text = text.replace(/\n(\d+\.)/g, '<br>$1').replace(/\n/g, '<br>');
                const listItems = text.split('<br>').map(item => item.trim()).filter(item => item.match(/^\d+\./));
                if (listItems.length > 0) {
                    const ol = document.createElement('ol');
                    ol.className = 'list-decimal list-inside space-y-1.5 pl-1';
                    listItems.forEach(itemText => {
                        const li = document.createElement('li');
                        li.textContent = itemText.replace(/^\d+\.\s*/, '');
                        ol.appendChild(li);
                    });
                    preventiveActionsContent.appendChild(ol);
                } else {
                     preventiveActionsContent.innerHTML = text.replace(/<br>/g, '\n');
                }
            } else {
                preventiveActionsContent.innerHTML = '<p>No suggestions received or unexpected format.</p>';
                preventiveActionsError.textContent = 'Could not parse suggestions from AI.';
                preventiveActionsError.classList.remove('hidden');
            }
        } catch (error) {
            console.error('Error fetching preventive actions:', error);
            preventiveActionsLoader.classList.add('hidden');
            preventiveActionsError.textContent = `Error: ${error.message}.`;
            preventiveActionsError.classList.remove('hidden');
            preventiveActionsContent.innerHTML = 'nut loose';
        }
    }

    async function fetchElaboratedRootCause(originalText) {
        analyzeLoader.classList.remove('hidden');
        analyzeRootCauseBtn.disabled = true;
        analyzeError.classList.add('hidden');
        analyzeError.textContent = '';

        const prompt = `You are an assistant helping with root cause analysis. A user has entered the following preliminary root cause description: "${originalText}".
Please analyze this description and provide an elaborated version. The elaborated version should be clearer, more detailed, and potentially identify 1-2 immediate contributing factors if obvious from the description.
Return only the elaborated root cause description, ready to be used in a text field. If the input is too vague, provide a slightly more detailed version of the input itself. Do not add any conversational preamble.`;

        const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };
        // const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${GEMINI_API_KEY}`;

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(`API request failed for elaboration: ${errorData?.error?.message || response.status}`);
            }
            const result = await response.json();
            if (result.candidates && result.candidates[0]?.content?.parts[0]?.text) {
                modalRootCauseTextarea.value = result.candidates[0].content.parts[0].text.trim();
                showNotification('Root cause elaborated by AI!', 'success');
            } else {
                throw new Error('Unexpected response structure from AI for elaboration.');
            }
        } catch (error) {
            console.error('Error elaborating root cause:', error);
            analyzeError.textContent = `AI Elaboration Error: ${error.message}`;
            analyzeError.classList.remove('hidden');
            showNotification('Failed to elaborate root cause with AI.', 'error');
        } finally {
            analyzeLoader.classList.add('hidden');
            analyzeRootCauseBtn.disabled = false;
        }
    }


    function renderTable() {
        let dataToRender = [...rootCausesData];
        const searchTerm = searchInput.value.toLowerCase();

        if (selectedCategory) {
            dataToRender = dataToRender.filter(rc => rc.category === selectedCategory);
        }
        if (selectedDefectGroup) {
            dataToRender = dataToRender.filter(rc => rc.defectGroup === selectedDefectGroup);
        }
        if (searchTerm) {
            dataToRender = dataToRender.filter(rc =>
                rc.code.toLowerCase().includes(searchTerm) ||
                rc.rootCause.toLowerCase().includes(searchTerm)
            );
        }

        currentFilteredDataForTable = dataToRender;

        // Store inline edit row data if it exists
        let inlineEditRowData = null;
        if (isInlineEditing) {
            const editRow = document.getElementById('inline-edit-row');
            if (editRow) {
                inlineEditRowData = {
                    code: document.getElementById('inline-code')?.value || '',
                    rootCause: document.getElementById('inline-rootcause')?.value || '',
                    category: document.getElementById('inline-category')?.value || '',
                    defectGroup: document.getElementById('inline-defectgroup')?.value || '',
                    isActive: document.getElementById('inline-isactive')?.checked || true
                };
            }
        }

        rootCauseTableBody.innerHTML = '';

        // Re-add inline edit row if it was there
        if (isInlineEditing && inlineEditRowData) {
            addInlineEditRowWithData(inlineEditRowData);
        }

        if (currentFilteredDataForTable.length === 0 && !isInlineEditing) {
            emptyState.classList.remove('hidden');
            paginationControls.classList.add('hidden');
            if (searchTerm || selectedCategory || selectedDefectGroup) {
                emptyStateTitle.textContent = "No Matching Root Causes";
                emptyStateMessage.textContent = "Try adjusting your search or filter criteria.";
            } else {
                emptyStateTitle.textContent = "No Root Causes Available";
                emptyStateMessage.textContent = "Get started by adding a new root cause.";
            }
            emptyStateBtnContainer.classList.remove('hidden');
        } else {
            emptyState.classList.add('hidden');
            paginationControls.classList.remove('hidden');
            const paginatedData = paginateData(currentFilteredDataForTable, currentPage, recordsPerPage);
            paginatedData.forEach(record => {
                const row = rootCauseTableBody.insertRow();
                row.className = 'hover:bg-sky-50 transition-colors duration-150';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-slate-800">${record.code}</td>
                    <td class="px-6 py-4 whitespace-normal text-sm text-slate-600 max-w-xs break-words">${record.rootCause}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-600">${record.category}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-slate-600">${record.defectGroup}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${record.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${record.isActive ? 'Active' : 'Inactive'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-center text-sm font-medium space-x-2">
                        <button class="text-purple-600 hover:text-purple-800 transition-colors duration-150 suggest-actions-btn" data-id="${record.id}" title="✨ Suggest Preventive Actions">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-magic" viewBox="0 0 16 16">
                                <path d="M9.5 2.672a.5.5 0 1 0 1 0V.843a.5.5 0 0 0-1 0v1.829Zm4.5.035A.5.5 0 0 0 13.293 2L12 3.293a.5.5 0 1 0 .707.707L14 2.707ZM7.293 4A.5.5 0 1 0 8 3.293L6.707 2A.5.5 0 0 0 6 2.707L7.293 4Zm-.621 2.5a.5.5 0 0 0 0 .708l.643.642a.5.5 0 0 0 .706-.706L7.38 6.5a.5.5 0 0 0-.707 0Z"/>
                                <path d="M12.43 8.87C12.29 9.842 11.556 11 10.5 11c-.93 0-1.61-.938-1.93-1.938a2.5 2.5 0 0 1 0-4.124C8.89 4.062 9.57 3 10.5 3c.944 0 1.71.933 1.93 1.938a2.5 2.5 0 0 1 0 4.124Zm-.315-5.067a.5.5 0 0 0-.7-.006L9.04 5.438a.5.5 0 1 0 .64.762l2.373-1.633a.5.5 0 0 0-.006-.7Z"/>
                                <path d="M10.5 13a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Zm4.5-2.5a.5.5 0 0 0-.5-.5h-2.5a.5.5 0 0 0 0 1h2.5a.5.5 0 0 0 .5-.5ZM13 4.5a.5.5 0 0 0 .5-.5v-2.5a.5.5 0 0 0-1 0v2.5a.5.5 0 0 0 .5.5ZM4.5 13a.5.5 0 0 0 .5.5h2.5a.5.5 0 0 0 0-1h-2.5a.5.5 0 0 0-.5.5ZM2 10.5a.5.5 0 0 0 .5.5h2.5a.5.5 0 0 0 0-1h-2.5a.5.5 0 0 0-.5.5Z"/>
                            </svg>
                        </button>
                        <button class="text-cyan-600 hover:text-cyan-800 transition-colors duration-150 edit-btn" data-id="${record.id}" title="Edit">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path><path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path></svg>
                        </button>
                        <button class="text-red-500 hover:text-red-700 transition-colors duration-150 delete-btn" data-id="${record.id}" data-code="${record.code}" title="Delete">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path><line x1="10" y1="11" x2="10" y2="17"></line><line x1="14" y1="11" x2="14" y2="17"></line></svg>
                        </button>
                    </td>
                `;
            });
        }
        updatePaginationControls();
    }

    rootCauseForm.addEventListener('submit', function(event) {
        event.preventDefault();
        const code = document.getElementById('modalCode').value.trim();
        const rootCause = modalRootCauseTextarea.value.trim();
        const category = modalCategorySelect.value;
        const defectGroup = modalDefectGroupSelect.value;
        const isActive = document.getElementById('modalIsActive').checked;

        if (!code || !rootCause || !category || !defectGroup) {
            showNotification('Please fill in all required fields.', 'error');
            return;
        }

        if (currentEditId) {
            const index = rootCausesData.findIndex(rc => rc.id === currentEditId);
            if (index > -1) {
                rootCausesData[index] = { ...rootCausesData[index], code, rootCause, category, defectGroup, isActive };
                showNotification('Root cause updated successfully!');
            }
        } else {
            const newId = rootCausesData.length > 0 ? Math.max(...rootCausesData.map(rc => rc.id)) + 1 : 1;
            rootCausesData.push({ id: newId, code, rootCause, category, defectGroup, isActive });
            showNotification('Root cause added successfully!');
        }
        updateFilterDropdownOptions();
        displayOverallSummary();
        renderTable();
        closeModal();
    });

    confirmDeleteBtn.addEventListener('click', () => {
        if (recordToDeleteId !== null) {
            rootCausesData = rootCausesData.filter(rc => rc.id !== recordToDeleteId);
            showNotification('Root cause deleted.', 'info');
            updateFilterDropdownOptions();
            displayOverallSummary();
            renderTable();
        }
        closeConfirmationModal();
    });

    rootCauseTableBody.addEventListener('click', function(event) {
        const target = event.target.closest('button');
        if (!target) return;
        const recordId = parseInt(target.dataset.id);
        const record = currentFilteredDataForTable.find(rc => rc.id === recordId);

        if (target.classList.contains('edit-btn')) {
            if (record) openModal('edit', record);
        } else if (target.classList.contains('delete-btn')) {
            const recordCode = target.dataset.code;
            if (record) openConfirmationModal(recordId, recordCode);
        } else if (target.classList.contains('suggest-actions-btn')) {
            if (record) openPreventiveActionsModal(record);
        }
    });

    function paginateData(data, page, perPage) {
        const start = (page - 1) * perPage;
        const end = start + perPage;
        return data.slice(start, end);
    }

    function updatePaginationControls() {
        const totalItems = currentFilteredDataForTable.length;
        const totalPages = Math.ceil(totalItems / recordsPerPage);
        const paginationControls = document.getElementById('paginationControls');

        if (totalItems === 0) {
            if (paginationControls) paginationControls.classList.add('hidden');
            return;
        }

        if (paginationControls) paginationControls.classList.remove('hidden');

        const showingFromElem = document.getElementById('showingFrom');
        const showingToElem = document.getElementById('showingTo');
        const totalRecordsElem = document.getElementById('totalRecords');
        const pageNumbersContainer = document.getElementById('pageNumbersContainer');
        const prevMobileBtn = document.getElementById('prevMobile');
        const nextMobileBtn = document.getElementById('nextMobile');
        const prevDesktopBtn = document.getElementById('prevDesktop');
        const nextDesktopBtn = document.getElementById('nextDesktop');


        const startItem = (currentPage - 1) * recordsPerPage + 1;
        const endItem = Math.min(currentPage * recordsPerPage, totalItems);

        if(showingFromElem) showingFromElem.textContent = startItem;
        if(showingToElem) showingToElem.textContent = endItem;
        if(totalRecordsElem) totalRecordsElem.textContent = totalItems;

        if(pageNumbersContainer) pageNumbersContainer.innerHTML = '';
        const maxPageButtons = 5;
        let startPage, endPage;

        if (totalPages <= maxPageButtons) {
            startPage = 1;
            endPage = totalPages;
        } else {
            const maxPagesBeforeCurrentPage = Math.floor(maxPageButtons / 2);
            const maxPagesAfterCurrentPage = Math.ceil(maxPageButtons / 2) - 1;
            if (currentPage <= maxPagesBeforeCurrentPage) {
                startPage = 1;
                endPage = maxPageButtons;
            } else if (currentPage + maxPagesAfterCurrentPage >= totalPages) {
                startPage = totalPages - maxPageButtons + 1;
                endPage = totalPages;
            } else {
                startPage = currentPage - maxPagesBeforeCurrentPage;
                endPage = currentPage + maxPagesAfterCurrentPage;
            }
        }

        if (startPage > 1 && pageNumbersContainer) {
            pageNumbersContainer.appendChild(createPageButton(1));
            if (startPage > 2) {
                 pageNumbersContainer.appendChild(createEllipsis());
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            if(pageNumbersContainer) pageNumbersContainer.appendChild(createPageButton(i));
        }

        if (endPage < totalPages && pageNumbersContainer) {
            if (endPage < totalPages - 1) {
                pageNumbersContainer.appendChild(createEllipsis());
            }
            pageNumbersContainer.appendChild(createPageButton(totalPages));
        }

        if(prevMobileBtn) prevMobileBtn.disabled = currentPage === 1;
        if(nextMobileBtn) nextMobileBtn.disabled = currentPage === totalPages || totalPages === 0;
        if(prevDesktopBtn) prevDesktopBtn.disabled = currentPage === 1;
        if(nextDesktopBtn) nextDesktopBtn.disabled = currentPage === totalPages || totalPages === 0;

        [prevMobileBtn, nextMobileBtn, prevDesktopBtn, nextDesktopBtn].forEach(btn => {
            if(btn) {
                btn.classList.toggle('opacity-60', btn.disabled);
                btn.classList.toggle('cursor-not-allowed', btn.disabled);
            }
        });
    }

    function createPageButton(pageNumber) {
        const button = document.createElement('button');
        button.textContent = pageNumber;
        button.className = `relative inline-flex items-center px-4 py-2.5 border border-slate-300 text-sm font-medium transition-colors duration-150 `;
        if (pageNumber === currentPage) {
            button.classList.add('z-10', 'bg-cyan-50', 'border-cyan-500', 'text-cyan-600', 'font-semibold');
        } else {
            button.classList.add('bg-white', 'text-slate-700', 'hover:bg-slate-50');
        }
        button.addEventListener('click', () => {
            currentPage = pageNumber;
            renderTable();
        });
        return button;
    }

    function createEllipsis() {
        const span = document.createElement('span');
        span.textContent = '...';
        span.className = 'relative inline-flex items-center px-4 py-2.5 border border-slate-300 bg-white text-sm font-medium text-slate-700';
        return span;
    }

    function changePage(direction) {
        const totalPages = Math.ceil(currentFilteredDataForTable.length / recordsPerPage);
        if (direction === 'next' && currentPage < totalPages) {
            currentPage++;
        } else if (direction === 'prev' && currentPage > 1) {
            currentPage--;
        }
        renderTable();
    }

    addRootCauseBtn.addEventListener('click', () => addInlineEditRow());
    emptyStateAddBtn.addEventListener('click', () => addInlineEditRow());

    cancelModalBtn.addEventListener('click', closeModal);
    modalBackdrop.addEventListener('click', closeModal);

    cancelDeleteBtn.addEventListener('click', closeConfirmationModal);
    confirmationModalBackdrop.addEventListener('click', closeConfirmationModal);

    closePreventiveActionsModalBtn.addEventListener('click', closePreventiveActionsModal);
    preventiveActionsModalBackdrop.addEventListener('click', closePreventiveActionsModal);

    analyzeRootCauseBtn.addEventListener('click', () => {
        const currentText = modalRootCauseTextarea.value;
        if (currentText.trim() === "") {
            analyzeError.textContent = "Please enter a root cause description first.";
            analyzeError.classList.remove('hidden');
            return;
        }
        fetchElaboratedRootCause(currentText);
    });


    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            if (!rootCauseModal.classList.contains('hidden')) closeModal();
            if (!confirmationModal.classList.contains('hidden')) closeConfirmationModal();
            if (!preventiveActionsModal.classList.contains('hidden')) closePreventiveActionsModal();
        }
    });

     refreshBtn.addEventListener('click', () => {
        // Cancel any ongoing inline editing
        if (isInlineEditing) {
            cancelInlineEdit();
        }

        // Clear all search inputs
        searchInput.value = '';
        categorySearchFilter.value = '';
        defectGroupSearchFilter.value = '';

        // Reset filter states
        selectedCategory = '';
        selectedDefectGroup = '';

        // Hide clear buttons and dropdowns
        categoryClearBtn.style.display = 'none';
        defectGroupClearBtn.style.display = 'none';
        categoryDropdown.classList.add('hidden');
        defectGroupDropdown.classList.add('hidden');

        currentPage = 1;
        displayOverallSummary();
        renderTable();
        showNotification('Data refreshed and filters reset.', 'info');
    });

    exportBtn.addEventListener('click', () => {
        const dataToExport = currentFilteredDataForTable;
        if (dataToExport.length === 0) {
            showNotification('No data to export.', 'info');
            return;
        }
        const headers = ['ID', 'Code', 'Root Cause', 'Category', 'Defect Group', 'Is Active'];
        const csvContent = [
            headers.join(','),
            ...dataToExport.map(row => [row.id, `"${row.code}"`, `"${row.rootCause.replace(/"/g, '""')}"`, `"${row.category}"`, `"${row.defectGroup}"`, row.isActive].join(','))
        ].join('\n');

        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        if (link.download !== undefined) {
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', 'root_causes_export.csv');
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            showNotification('Data exported successfully.');
        } else {
            showNotification('CSV export not supported by your browser.', 'error');
        }
    });

    prevMobileBtn.addEventListener('click', () => changePage('prev'));
    nextMobileBtn.addEventListener('click', () => changePage('next'));
    prevDesktopBtn.addEventListener('click', () => changePage('prev'));
    nextDesktopBtn.addEventListener('click', () => changePage('next'));

    categoryFilterSelect.addEventListener('change', () => {
        currentPage = 1;
        renderTable();
    });
    defectGroupFilterSelect.addEventListener('change', () => {
        currentPage = 1;
        renderTable();
    });
     searchInput.addEventListener('input', () => {
        currentPage = 1;
        renderTable();
    });

    document.addEventListener('DOMContentLoaded', () => {
        updateFilterDropdownOptions();

        // Initialize search filters
        initializeSearchFilters();

        // Initialize modal custom dropdowns
        initializeCustomDropdown('modalCategory');
        initializeCustomDropdown('modalDefectGroup');

        displayOverallSummary();
        renderTable();
    });
</script>
</body>
</html>
