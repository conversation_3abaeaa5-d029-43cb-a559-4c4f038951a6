<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Defect Management System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #374151;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Main Container */
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            padding: 3rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            text-align: center;
            max-width: 600px;
            width: 90%;
            position: relative;
            overflow: hidden;
        }

        .main-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4);
        }

        /* Header Section */
        .header-section {
            margin-bottom: 3rem;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }

        .logo span {
            font-size: 2rem;
            font-weight: 700;
            color: white;
        }

        .main-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #1e293b, #3b82f6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .main-subtitle {
            font-size: 1.125rem;
            color: #64748b;
            margin-bottom: 0.5rem;
        }

        .main-description {
            font-size: 0.875rem;
            color: #94a3b8;
            max-width: 400px;
            margin: 0 auto;
        }

        /* Navigation Buttons */
        .nav-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .nav-btn {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 2rem 1.5rem;
            text-decoration: none;
            color: #1e293b;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
        }

        .nav-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-btn:hover::before {
            left: 100%;
        }

        .nav-btn:hover {
            transform: translateY(-4px);
            border-color: #3b82f6;
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
        }

        .nav-btn.primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            border-color: #3b82f6;
            color: white;
        }

        .nav-btn.primary:hover {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.4);
        }

        .nav-btn.secondary {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-color: #cbd5e1;
        }

        .nav-btn.secondary:hover {
            background: linear-gradient(135deg, #e2e8f0, #cbd5e1);
            border-color: #94a3b8;
        }

        /* Icon Styles */
        .nav-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
        }

        .nav-btn.secondary .nav-icon {
            background: rgba(59, 130, 246, 0.1);
        }

        .nav-icon svg {
            width: 24px;
            height: 24px;
        }

        .nav-btn.primary .nav-icon svg {
            color: white;
        }

        .nav-btn.secondary .nav-icon svg {
            color: #3b82f6;
        }

        /* Text Styles */
        .nav-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .nav-description {
            font-size: 0.875rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .main-container {
                padding: 2rem;
                margin: 1rem;
            }

            .nav-buttons {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .nav-btn {
                padding: 1.5rem 1rem;
            }

            .main-title {
                font-size: 2rem;
            }

            .main-subtitle {
                font-size: 1rem;
            }
        }

        /* Animation */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .main-container {
            animation: fadeInUp 0.6s ease-out;
        }

        .nav-btn {
            animation: fadeInUp 0.6s ease-out;
        }

        .nav-btn:nth-child(1) {
            animation-delay: 0.1s;
        }

        .nav-btn:nth-child(2) {
            animation-delay: 0.2s;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header Section -->
        <div class="header-section">
            <div class="logo">
                <span>DM</span>
            </div>
            <h1 class="main-title">Defect Management</h1>
            <p class="main-subtitle">Comprehensive Quality Control System</p>
            <p class="main-description">
                Manage and track defects efficiently with our integrated management system. 
                Choose your preferred management approach below.
            </p>
        </div>

        <!-- Navigation Buttons -->
        <div class="nav-buttons">
            <!-- Defect Group Button -->
            <a href="DefectGroupindex.html" class="nav-btn primary">
                <div class="nav-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <div>
                    <div class="nav-title">Defect Group</div>
                    <div class="nav-description">Manage defect groups by categories and organize quality issues</div>
                </div>
            </a>

            <!-- Defect Name Button -->
            <a href="DefectNameIndex.html" class="nav-btn secondary">
                <div class="nav-icon">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div>
                    <div class="nav-title">Defect Name</div>
                    <div class="nav-description">Manage individual defect names and detailed classifications</div>
                </div>
            </a>
        </div>
    </div>
</body>
</html>
